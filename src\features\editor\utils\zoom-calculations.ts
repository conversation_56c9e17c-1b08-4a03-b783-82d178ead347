import { IZoomConfig, IZoomTiming } from "../store/use-zoom-store";
import { CursorOverlayData, CursorPoint } from "../../remotion/CursorOverlay";

/**
 * Cubic bezier helper function for smooth zoom animations
 * This creates smooth acceleration and deceleration curves
 *
 * @param t - Progress value between 0 and 1
 * @param p1 - First control point
 * @param p2 - Second control point
 * @param p3 - Third control point
 * @param p4 - Fourth control point
 * @returns Bezier curve value
 */
export function cubicBezier(t: number, p1: number, p2: number, p3: number, p4: number): number {
  const u = 1 - t;
  const tt = t * t;
  const uu = u * u;
  const uuu = uu * u;
  const ttt = tt * t;
  return 3 * uu * t * p2 + 3 * u * tt * p3 + ttt;
}

/**
 * Easing functions for zoom-out animation
 */
export function easeOut(t: number): number {
  return 1 - Math.pow(1 - t, 3);
}

export function linear(t: number): number {
  return t;
}

/**
 * Parameters for zoom scale calculation
 */
export interface IZoomCalculationParams {
  /** Current time or frame position */
  currentPosition: number;
  /** Whether currentPosition is in frames (true) or milliseconds (false) */
  isFrameBased: boolean;
  /** Frames per second (required if isFrameBased is true) */
  fps?: number;
  /** Zoom timing configuration */
  zoomTiming: IZoomTiming;
  /** Zoom configuration settings */
  zoomConfig: IZoomConfig;
}

/**
 * Result of zoom scale calculation
 */
export interface IZoomCalculationResult {
  /** The calculated zoom scale (1.0 = no zoom) */
  zoomScale: number;
  /** Whether zoom is currently active */
  isZoomActive: boolean;
  /** Progress through the zoom animation (0-1) */
  progress: number;
  /** Bezier-adjusted progress value */
  bezierProgress: number;
  /** Current zoom phase: 'zoom-in', 'zoom-out', or 'inactive' */
  phase: 'zoom-in' | 'zoom-out' | 'inactive';
}

/**
 * Centralized zoom scale calculation function
 * This eliminates the duplicated zoom logic between VideoEditorComposition and canvas-container
 * 
 * @param params - Zoom calculation parameters
 * @returns Zoom calculation result
 */
export function calculateZoomScale(params: IZoomCalculationParams): IZoomCalculationResult {
  const { currentPosition, isFrameBased, fps, zoomTiming, zoomConfig } = params;

  // Convert position to time in milliseconds if needed
  let currentTime: number;
  if (isFrameBased) {
    if (!fps) {
      throw new Error("FPS is required when using frame-based calculations");
    }
    currentTime = (currentPosition / fps) * 1000;
  } else {
    currentTime = currentPosition;
  }

  // Extract timing values
  const zoomStartTime = zoomTiming.startTime;
  const zoomEndTime = zoomTiming.endTime;
  const zoomDuration = zoomEndTime - zoomStartTime;

  // Early return for performance if outside zoom range
  if (zoomDuration <= 0) {
    return {
      zoomScale: 1,
      isZoomActive: false,
      progress: 0,
      bezierProgress: 0,
      phase: 'inactive'
    };
  }

  // Calculate zoom-out end time
  const zoomOutDuration = zoomConfig.zoomOut.enabled ? zoomConfig.zoomOut.duration : 0;
  const zoomOutEndTime = zoomEndTime + zoomOutDuration;

  // Early return if completely outside zoom range for better performance
  if (currentTime < zoomStartTime && (!zoomConfig.zoomOut.enabled || currentTime > zoomOutEndTime)) {
    return {
      zoomScale: 1,
      isZoomActive: false,
      progress: 0,
      bezierProgress: 0,
      phase: 'inactive'
    };
  }

  // Initialize result
  let zoomScale = 1;
  let isZoomActive = false;
  let progress = 0;
  let bezierProgress = 0;
  let phase: 'zoom-in' | 'zoom-out' | 'inactive' = 'inactive';

  // Check if we're in the zoom-in phase
  if (currentTime >= zoomStartTime && currentTime <= zoomEndTime) {
    isZoomActive = true;
    phase = 'zoom-in';

    // Calculate progress through the zoom-in animation (0 to 1)
    progress = (currentTime - zoomStartTime) / zoomDuration;

    // Use sine wave to create smooth zoom in and out within the duration
    // sin(0) = 0, sin(π/2) = 1, sin(π) = 0
    // This creates a smooth zoom that peaks in the middle and returns to 1.0 at the end
    bezierProgress = Math.sin(progress * Math.PI);

    // Calculate final zoom scale
    zoomScale = 1 + bezierProgress * zoomConfig.maxZoomScale;
  }
  // Optional zoom-out phase: only used if someone wants additional zoom-out after the main cycle
  // Note: The main zoom cycle now completes within the zoom duration using sine wave
  else if (zoomConfig.zoomOut.enabled && zoomOutDuration > 0 &&
           currentTime > zoomEndTime && currentTime <= zoomOutEndTime) {
    isZoomActive = true;
    phase = 'zoom-out';

    // Calculate progress through the zoom-out animation (0 to 1)
    progress = (currentTime - zoomEndTime) / zoomOutDuration;

    // Apply easing function based on configuration
    let easedProgress: number;
    switch (zoomConfig.zoomOut.easing) {
      case 'linear':
        easedProgress = linear(progress);
        break;
      case 'ease-out':
        easedProgress = easeOut(progress);
        break;
      case 'bezier':
        const { p1, p2, p3, p4 } = zoomConfig.bezierControlPoints;
        easedProgress = cubicBezier(progress, p1, p2, p3, p4);
        break;
      default:
        easedProgress = easeOut(progress);
    }

    bezierProgress = easedProgress;

    // Since the main zoom cycle already returned to 1.0, this phase starts from 1.0
    // and can zoom out further if desired (scale < 1.0) or do nothing
    zoomScale = 1.0; // Keep at normal scale since main cycle already completed
  }

  return {
    zoomScale,
    isZoomActive,
    progress,
    bezierProgress,
    phase,
  };
}

/**
 * Convenience function for frame-based zoom calculation (used by Remotion)
 * 
 * @param currentFrame - Current frame number
 * @param fps - Frames per second
 * @param zoomTiming - Zoom timing configuration
 * @param zoomConfig - Zoom configuration settings
 * @returns Zoom calculation result
 */
export function calculateZoomScaleFromFrame(
  currentFrame: number,
  fps: number,
  zoomTiming: IZoomTiming,
  zoomConfig: IZoomConfig
): IZoomCalculationResult {
  return calculateZoomScale({
    currentPosition: currentFrame,
    isFrameBased: true,
    fps,
    zoomTiming,
    zoomConfig,
  });
}

/**
 * Convenience function for time-based zoom calculation (used by Player)
 * 
 * @param currentTime - Current time in milliseconds
 * @param zoomTiming - Zoom timing configuration
 * @param zoomConfig - Zoom configuration settings
 * @returns Zoom calculation result
 */
export function calculateZoomScaleFromTime(
  currentTime: number,
  zoomTiming: IZoomTiming,
  zoomConfig: IZoomConfig
): IZoomCalculationResult {
  return calculateZoomScale({
    currentPosition: currentTime,
    isFrameBased: false,
    zoomTiming,
    zoomConfig,
  });
}

/**
 * Get cursor position at a specific frame from cursor overlay data
 *
 * @param cursorData - Cursor overlay data from extension
 * @param frame - Current frame number
 * @returns Cursor position or null if no cursor data available
 */
export function getCursorPositionAtFrame(
  cursorData: CursorOverlayData | undefined,
  frame: number
): CursorPoint | null {
  if (!cursorData || !cursorData.frameData) {
    return null;
  }

  // Find the most recent cursor position at or before current frame
  for (let f = frame; f >= 0; f--) {
    const framePoints = cursorData.frameData[f];
    if (framePoints && framePoints.length > 0) {
      return framePoints[framePoints.length - 1];
    }
  }

  return null;
}

/**
 * Calculate zoom area based on cursor position with smooth interpolation
 *
 * @param cursorPosition - Current cursor position
 * @param previousZoomArea - Previous zoom area for smoothing
 * @param cursorConfig - Cursor following configuration
 * @param canvasWidth - Canvas width for normalization
 * @param canvasHeight - Canvas height for normalization
 * @returns New zoom area centered on cursor position
 */
export function calculateCursorFollowingZoomArea(
  cursorPosition: CursorPoint,
  previousZoomArea: IZoomConfig['zoomArea'] | null,
  cursorConfig: IZoomConfig['cursorFollowing'],
  canvasWidth: number,
  canvasHeight: number
): IZoomConfig['zoomArea'] {
  // Normalize cursor position to 0-1 coordinates
  const normalizedX = cursorPosition.x / canvasWidth;
  const normalizedY = cursorPosition.y / canvasHeight;

  // Apply offset
  const targetCenterX = normalizedX + cursorConfig.offset.x;
  const targetCenterY = normalizedY + cursorConfig.offset.y;

  // Calculate target zoom area centered on cursor
  const halfWidth = cursorConfig.followAreaSize.width / 2;
  const halfHeight = cursorConfig.followAreaSize.height / 2;

  let targetX = targetCenterX - halfWidth;
  let targetY = targetCenterY - halfHeight;

  // Apply edge padding constraints
  const padding = cursorConfig.edgePadding;
  targetX = Math.max(padding, Math.min(1 - cursorConfig.followAreaSize.width - padding, targetX));
  targetY = Math.max(padding, Math.min(1 - cursorConfig.followAreaSize.height - padding, targetY));

  const targetZoomArea = {
    x: targetX,
    y: targetY,
    width: cursorConfig.followAreaSize.width,
    height: cursorConfig.followAreaSize.height,
  };

  // Apply smoothing if we have a previous zoom area
  if (previousZoomArea && cursorConfig.smoothing > 0) {
    const smoothing = cursorConfig.smoothing;

    // Check if cursor moved enough to warrant an update
    const deltaX = Math.abs(targetZoomArea.x - previousZoomArea.x);
    const deltaY = Math.abs(targetZoomArea.y - previousZoomArea.y);
    const totalDelta = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    if (totalDelta < cursorConfig.movementThreshold) {
      // Movement too small, keep previous area
      return previousZoomArea;
    }

    // Apply smoothing interpolation
    return {
      x: previousZoomArea.x + (targetZoomArea.x - previousZoomArea.x) * (1 - smoothing),
      y: previousZoomArea.y + (targetZoomArea.y - previousZoomArea.y) * (1 - smoothing),
      width: targetZoomArea.width,
      height: targetZoomArea.height,
    };
  }

  return targetZoomArea;
}

/**
 * Enhanced zoom calculation parameters that include cursor following support
 */
export interface ICursorFollowingZoomParams extends IZoomCalculationParams {
  /** Cursor overlay data from extension */
  cursorData?: CursorOverlayData;
  /** Canvas dimensions for cursor position normalization */
  canvasDimensions?: {
    width: number;
    height: number;
  };
  /** Previous zoom area for smoothing (optional) */
  previousZoomArea?: IZoomConfig['zoomArea'];
}

/**
 * Enhanced zoom calculation result that includes cursor following information
 */
export interface ICursorFollowingZoomResult extends IZoomCalculationResult {
  /** Whether cursor following is active */
  isCursorFollowing: boolean;
  /** Current cursor position if available */
  cursorPosition?: CursorPoint;
  /** Calculated zoom area (may be cursor-following or fixed) */
  zoomArea: IZoomConfig['zoomArea'];
}

/**
 * Calculate zoom scale and area with cursor following support
 *
 * @param params - Enhanced zoom calculation parameters
 * @returns Enhanced zoom calculation result with cursor following
 */
export function calculateCursorFollowingZoom(
  params: ICursorFollowingZoomParams
): ICursorFollowingZoomResult {
  const { cursorData, canvasDimensions, previousZoomArea, zoomConfig } = params;

  // Get base zoom calculation
  const baseResult = calculateZoomScale(params);

  // Initialize result with base values
  let result: ICursorFollowingZoomResult = {
    ...baseResult,
    isCursorFollowing: false,
    zoomArea: zoomConfig.zoomArea, // Default to fixed zoom area
  };

  // Check if cursor following is enabled and we have the required data
  if (
    zoomConfig.cursorFollowing.enabled &&
    cursorData &&
    canvasDimensions &&
    baseResult.isZoomActive
  ) {
    // Convert current position to frame if needed
    let currentFrame: number;
    if (params.isFrameBased) {
      currentFrame = params.currentPosition;
    } else {
      // Convert time to frame
      currentFrame = Math.floor((params.currentPosition / 1000) * (params.fps || 30));
    }

    // Get cursor position at current frame
    const cursorPosition = getCursorPositionAtFrame(cursorData, currentFrame);

    if (cursorPosition) {
      // Calculate cursor-following zoom area
      const cursorZoomArea = calculateCursorFollowingZoomArea(
        cursorPosition,
        previousZoomArea,
        zoomConfig.cursorFollowing,
        canvasDimensions.width,
        canvasDimensions.height
      );

      result = {
        ...result,
        isCursorFollowing: true,
        cursorPosition,
        zoomArea: cursorZoomArea,
      };
    }
  }

  return result;
}
