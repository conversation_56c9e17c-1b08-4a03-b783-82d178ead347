import React, { useState, useRef, useCallback, useEffect } from "react";
import { timeMsToUnits, unitsToTimeMs } from "../utils/timeline";
import { TIMELINE_OFFSET_CANVAS_LEFT } from "../constants/constants";
import useStore from "../store/use-store";
import { X, AlertTriangle } from "lucide-react";

// Color palette for zoom effects
const ZOOM_EFFECT_COLORS = [
  { bg: 'bg-blue-500/30', bgSelected: 'bg-blue-500/40', border: 'border-blue-400', borderSelected: 'border-blue-500', gradient: 'from-blue-500/20 to-blue-600/20', name: 'Blue' },
  { bg: 'bg-green-500/30', bgSelected: 'bg-green-500/40', border: 'border-green-400', borderSelected: 'border-green-500', gradient: 'from-green-500/20 to-green-600/20', name: 'Green' },
  { bg: 'bg-purple-500/30', bgSelected: 'bg-purple-500/40', border: 'border-purple-400', borderSelected: 'border-purple-500', gradient: 'from-purple-500/20 to-purple-600/20', name: 'Purple' },
  { bg: 'bg-pink-500/30', bgSelected: 'bg-pink-500/40', border: 'border-pink-400', borderSelected: 'border-pink-500', gradient: 'from-pink-500/20 to-pink-600/20', name: 'Pink' },
  { bg: 'bg-indigo-500/30', bgSelected: 'bg-indigo-500/40', border: 'border-indigo-400', borderSelected: 'border-indigo-500', gradient: 'from-indigo-500/20 to-indigo-600/20', name: 'Indigo' },
  { bg: 'bg-teal-500/30', bgSelected: 'bg-teal-500/40', border: 'border-teal-400', borderSelected: 'border-teal-500', gradient: 'from-teal-500/20 to-teal-600/20', name: 'Teal' },
  { bg: 'bg-amber-500/30', bgSelected: 'bg-amber-500/40', border: 'border-amber-400', borderSelected: 'border-amber-500', gradient: 'from-amber-500/20 to-amber-600/20', name: 'Amber' },
  { bg: 'bg-rose-500/30', bgSelected: 'bg-rose-500/40', border: 'border-rose-400', borderSelected: 'border-rose-500', gradient: 'from-rose-500/20 to-rose-600/20', name: 'Rose' },
];

// Function to get color for a zoom effect based on its index
const getZoomEffectColor = (index: number) => {
  return ZOOM_EFFECT_COLORS[index % ZOOM_EFFECT_COLORS.length];
};

interface ZoomEffectOverlayProps {
  isActive: boolean;
  onClose: () => void;
  scrollLeft: number;
}

const ZoomEffectOverlay: React.FC<ZoomEffectOverlayProps> = ({
  isActive,
  onClose,
  scrollLeft,
}) => {
  const {
    zoomEffects,
    selectedZoomEffectId,
    updateZoomEffect,
    removeZoomEffect,
    selectZoomEffect,
    getOverlappingZoomEffects,
    scale,
    duration
  } = useStore();

  const [dragState, setDragState] = useState<{
    type: 'start' | 'end' | 'bar' | null;
    effectId: string | null;
    startX: number;
    originalStartTime: number;
    originalEndTime: number;
  }>({
    type: null,
    effectId: null,
    startX: 0,
    originalStartTime: 0,
    originalEndTime: 0,
  });

  // Local state for smooth dragging without store updates
  const [localDragPosition, setLocalDragPosition] = useState<{
    startTime: number;
    endTime: number;
  } | null>(null);

  // Ref to track animation frame for throttling
  const animationFrameRef = useRef<number | null>(null);

  const overlayRef = useRef<HTMLDivElement>(null);

  // Convert time to position
  const getPositionFromTime = useCallback((time: number) => {
    return timeMsToUnits(time, scale.zoom) - scrollLeft;
  }, [scale.zoom, scrollLeft]);



  // Handle mouse events for zoom effect handles and bars
  const handleMouseDown = useCallback((
    e: React.MouseEvent,
    type: 'start' | 'end' | 'bar',
    effectId: string,
    effect: any
  ) => {
    e.preventDefault();
    e.stopPropagation();

    selectZoomEffect(effectId);

    setDragState({
      type,
      effectId,
      startX: e.clientX,
      originalStartTime: effect.startTime,
      originalEndTime: effect.endTime,
    });

    // Clear any previous local drag position
    setLocalDragPosition(null);
  }, [selectZoomEffect]);

  // Handle mouse move - use local state for smooth dragging with requestAnimationFrame
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!dragState.type || !dragState.effectId) return;

    // Cancel previous animation frame if it exists
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    // Schedule update on next frame for smooth performance
    animationFrameRef.current = requestAnimationFrame(() => {
      const deltaX = e.clientX - dragState.startX;
      const deltaTime = unitsToTimeMs(deltaX, scale.zoom);

      if (dragState.type === 'start') {
        const newStartTime = Math.max(0, Math.min(duration, dragState.originalStartTime + deltaTime));

        if (newStartTime < dragState.originalEndTime - 100) {
          setLocalDragPosition({
            startTime: newStartTime,
            endTime: dragState.originalEndTime
          });
        }
      } else if (dragState.type === 'end') {
        const newEndTime = Math.max(0, Math.min(duration, dragState.originalEndTime + deltaTime));

        if (newEndTime > dragState.originalStartTime + 100) {
          setLocalDragPosition({
            startTime: dragState.originalStartTime,
            endTime: newEndTime
          });
        }
      } else if (dragState.type === 'bar') {
        const effectDuration = dragState.originalEndTime - dragState.originalStartTime;
        const newStartTime = Math.max(0, Math.min(duration - effectDuration, dragState.originalStartTime + deltaTime));
        const newEndTime = newStartTime + effectDuration;

        if (newEndTime <= duration) {
          setLocalDragPosition({
            startTime: newStartTime,
            endTime: newEndTime
          });
        }
      }
    });
  }, [dragState, scale.zoom, duration, setLocalDragPosition]);

  // Handle mouse up - apply final position to store
  const handleMouseUp = useCallback(() => {
    // Cancel any pending animation frame
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    if (dragState.effectId && localDragPosition) {
      updateZoomEffect(dragState.effectId, {
        startTime: localDragPosition.startTime,
        endTime: localDragPosition.endTime
      });
    }

    setDragState({
      type: null,
      effectId: null,
      startX: 0,
      originalStartTime: 0,
      originalEndTime: 0,
    });
    setLocalDragPosition(null);
  }, [dragState.effectId, localDragPosition, updateZoomEffect, setLocalDragPosition]);

  // Add global mouse event listeners
  useEffect(() => {
    if (dragState.type) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [dragState.type, handleMouseMove, handleMouseUp]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        selectZoomEffect(null);
      }
      if (e.key === 'Delete' && selectedZoomEffectId) {
        removeZoomEffect(selectedZoomEffectId);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [selectZoomEffect, removeZoomEffect, selectedZoomEffectId]);

  // Cleanup animation frame on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  if (!isActive) return null;

  return (
    <div
      ref={overlayRef}
      className="absolute inset-0 z-20 pointer-events-none"
      style={{ top: 50, height: "calc(100% - 40px)" }}
    >


      {/* Render all zoom effects */}
      {zoomEffects.map((effect, index) => {
        // Use local drag position if this effect is being dragged
        const isDragging = dragState.effectId === effect.id && localDragPosition;
        const effectStartTime = isDragging ? localDragPosition.startTime : effect.startTime;
        const effectEndTime = isDragging ? localDragPosition.endTime : effect.endTime;

        const startPosition = getPositionFromTime(effectStartTime);
        const endPosition = getPositionFromTime(effectEndTime);
        const barWidth = endPosition - startPosition;
        const isSelected = selectedZoomEffectId === effect.id;
        const overlappingEffects = getOverlappingZoomEffects(effect.id);
        const hasConflicts = overlappingEffects.length > 0;

        // Get color for this effect
        const effectColor = getZoomEffectColor(index);

        return (
          <div key={effect.id}>
            {/* Zoom effect bar */}
            <div
              className={`absolute pointer-events-auto cursor-move ${
                isDragging ? '' : 'transition-all'
              } ${
                isSelected
                  ? 'ring-2 ring-blue-400 ring-offset-1'
                  : hasConflicts
                  ? 'ring-2 ring-orange-400 ring-offset-1'
                  : 'hover:ring-1 hover:ring-blue-300'
              }`}
              style={{
                left: 40 + TIMELINE_OFFSET_CANVAS_LEFT + startPosition,
                top: 35,
                width: Math.max(barWidth, 20),
                height: 32,
              }}
              onMouseDown={(e) => handleMouseDown(e, 'bar', effect.id, effect)}
              onClick={() => selectZoomEffect(effect.id)}
              onDoubleClick={(e) => {
                e.stopPropagation();
                updateZoomEffect(effect.id, {
                  ...effect,
                  cursorFollowing: !effect.cursorFollowing
                });
              }}
              title={hasConflicts
                ? `${effectColor.name} Zoom - Overlaps with ${overlappingEffects.length} other zoom effect(s)`
                : effect.cursorFollowing
                  ? `${effectColor.name} Cursor Following Zoom Effect - Dynamically follows cursor movement. Double-click to switch to fixed zoom.`
                  : `${effectColor.name} Fixed Zoom Effect - Double-click to enable cursor following.`
              }
            >
              {/* Effect bar background */}
              <div className={`absolute inset-0 rounded-md backdrop-blur-sm border-2 ${
                isSelected
                  ? `${effectColor.bgSelected} ${effectColor.borderSelected}`
                  : hasConflicts
                  ? 'bg-orange-500/40 border-orange-500'
                  : `${effectColor.bg} ${effectColor.border}`
              }`}>
                <div className={`absolute inset-0 bg-gradient-to-r rounded-sm ${
                  hasConflicts
                    ? 'from-orange-500/20 to-orange-600/20'
                    : effectColor.gradient
                }`} />
              </div>

              {/* Effect indicator */}
              <div className="absolute inset-0 flex items-center justify-center">
                {hasConflicts ? (
                  <AlertTriangle size={12} className="text-orange-200 drop-shadow-sm" />
                ) : effect.cursorFollowing ? (
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                    <span className="text-xs text-white font-medium drop-shadow-sm">CURSOR</span>
                  </div>
                ) : (
                  <div className="text-xs text-white font-medium drop-shadow-sm opacity-70">ZOOM</div>
                )}
              </div>

              {/* Delete button for selected effect */}
              {isSelected && (
                <button
                  className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeZoomEffect(effect.id);
                  }}
                >
                  <X size={10} />
                </button>
              )}
            </div>

            {/* Start handle */}
            {isSelected && (
              <div
                className="absolute pointer-events-auto cursor-ew-resize group"
                style={{
                  left: 40 + TIMELINE_OFFSET_CANVAS_LEFT + startPosition,
                  top: 35,
                  width: 12,
                  height: 32,
                  transform: "translateX(-50%)",
                }}
                onMouseDown={(e) => handleMouseDown(e, 'start', effect.id, effect)}
              >
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="w-3 h-6 bg-blue-500 border border-blue-600 rounded-sm shadow-lg group-hover:bg-blue-400 transition-colors" />
                </div>
              </div>
            )}

            {/* End handle */}
            {isSelected && (
              <div
                className="absolute pointer-events-auto cursor-ew-resize group"
                style={{
                  left: 40 + TIMELINE_OFFSET_CANVAS_LEFT + endPosition,
                  top: 35,
                  width: 12,
                  height: 32,
                  transform: "translateX(-50%)",
                }}
                onMouseDown={(e) => handleMouseDown(e, 'end', effect.id, effect)}
              >
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="w-3 h-6 bg-green-500 border border-green-600 rounded-sm shadow-lg group-hover:bg-green-400 transition-colors" />
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default ZoomEffectOverlay;
